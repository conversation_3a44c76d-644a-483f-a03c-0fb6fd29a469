# GD32F470VE UART1 RS485 通信配置说明

## 硬件连接

### 主控芯片引脚配置
- **PA1**: DE/RE控制引脚 (GPIO输出)
- **PA2**: UART1_TX (复用功能AF4)
- **PA3**: UART1_RX (复用功能AF4)

### MAX3485芯片连接
```
GD32F470VE    MAX3485
PA2 (TX)  --> R (接收输入)
PA3 (RX)  <-- T (发送输出)
PA1       --> DE/RE (方向控制)
```

### RS485总线连接
```
MAX3485    RS485总线
A+     --> A+
B-     --> B-
GND    --> GND
```

### USB转RS485模块连接
```
RS485总线    USB转RS485
A+      --> A+
B-      --> B-
GND     --> GND
```

## 软件配置

### 1. 初始化函数
```c
#include "CMIC_GD32f470vet6.h"
#include "rs485_example.h"

int main(void)
{
    // 系统初始化
    systick_config();
    
    // RS485初始化
    rs485_example_init();
    
    while(1)
    {
        rs485_main_loop();
    }
}
```

### 2. 发送数据
```c
uint8_t data[] = "Hello RS485!";
rs485_send_data(data, strlen((char*)data));
```

### 3. 接收数据处理
```c
void process_rs485_data(void)
{
    if(rs485_is_frame_complete())
    {
        uint16_t len = rs485_get_rx_count();
        uint8_t* buffer = rs485_get_rx_buffer();
        
        // 处理接收到的数据
        for(uint16_t i = 0; i < len; i++)
        {
            // 处理buffer[i]
        }
        
        // 清除接收缓冲区
        rs485_clear_rx_buffer();
    }
}
```

## 配置参数

### UART1配置
- **波特率**: 9600 bps (可在bsp_uart1_rs485_init()中修改)
- **数据位**: 8位
- **停止位**: 1位
- **校验位**: 无
- **流控制**: 无

### 中断配置
- **USART1_IRQn**: 优先级1，子优先级0
- **接收中断**: USART_INT_RBNE
- **空闲中断**: USART_INT_IDLE (用于帧检测)

## API函数说明

### 初始化函数
- `bsp_uart1_rs485_init()`: 初始化UART1和GPIO
- `rs485_example_init()`: 初始化RS485通信示例

### 控制函数
- `rs485_send_enable()`: 切换到发送模式
- `rs485_receive_enable()`: 切换到接收模式

### 数据传输函数
- `rs485_send_data(uint8_t *data, uint16_t length)`: 发送数据
- `rs485_receive_data(uint8_t *buffer, uint16_t max_length)`: 非阻塞接收数据

### 状态查询函数
- `rs485_get_rx_count()`: 获取接收数据长度
- `rs485_is_frame_complete()`: 检查是否接收完整帧
- `rs485_get_rx_buffer()`: 获取接收缓冲区指针
- `rs485_clear_rx_buffer()`: 清除接收缓冲区

## 注意事项

1. **方向控制时序**: 
   - 发送前需要先调用`rs485_send_enable()`
   - 发送完成后自动切换回接收模式

2. **中断处理**:
   - USART1中断处理函数已在rs485_example.c中实现
   - 使用IDLE中断检测帧结束

3. **缓冲区管理**:
   - 接收缓冲区大小为256字节
   - 需要及时处理接收到的数据避免溢出

4. **波特率设置**:
   - 默认9600bps，可根据需要修改
   - 确保通信双方波特率一致

5. **硬件连接**:
   - 确保MAX3485的VCC连接到3.3V或5V
   - A+和B-线需要使用双绞线
   - 长距离通信时需要在总线两端添加120Ω终端电阻

## 测试方法

1. 使用USB转RS485模块连接到电脑
2. 打开串口调试助手，设置为9600-8-N-1
3. 运行程序，应该能看到定期发送的测试数据
4. 从电脑发送数据，程序会回显接收到的数据

## 故障排除

1. **无法发送数据**:
   - 检查PA1、PA2引脚连接
   - 确认MAX3485供电正常
   - 检查DE/RE控制逻辑

2. **无法接收数据**:
   - 检查PA3引脚连接
   - 确认RS485总线A+、B-连接正确
   - 检查波特率设置

3. **数据错误**:
   - 检查总线终端电阻
   - 确认通信双方波特率一致
   - 检查线路干扰
