# GD32F470VE UART1 RS485配置总结

## 配置完成的文件

### 1. 头文件修改
- **文件**: `2025923314-code/GD321/Components/bsp/CMIC_GD32f470vet6.h`
- **修改内容**: 添加了UART1 RS485相关的宏定义和函数声明

### 2. 源文件修改  
- **文件**: `2025923314-code/GD321/Components/bsp/CMIC_GD32f470vet6.c`
- **修改内容**: 添加了UART1 RS485初始化和控制函数实现

### 3. 新增文件
- `rs485_example.c` - RS485通信示例实现
- `rs485_example.h` - RS485通信示例头文件
- `main_rs485_demo.c` - 主程序演示文件
- `RS485_README.md` - 详细使用说明文档

## 引脚配置

| 引脚 | 功能 | 复用功能 | 连接 |
|------|------|----------|------|
| PA1  | DE/RE控制 | GPIO输出 | MAX3485的DE/RE引脚 |
| PA2  | UART1_TX | AF4 | MAX3485的R引脚 |
| PA3  | UART1_RX | AF4 | MAX3485的T引脚 |

## MAX3485连接

```
GD32F470VE          MAX3485          RS485总线
PA2 (UART1_TX) ---> R (接收输入)
PA3 (UART1_RX) <--- T (发送输出)
PA1 (GPIO)     ---> DE/RE (方向控制)
                    A+ ------------> A+
                    B- ------------> B-
                    GND -----------> GND
```

## 主要函数

### 初始化函数
```c
void bsp_uart1_rs485_init(void);    // UART1硬件初始化
void rs485_example_init(void);       // RS485示例初始化
```

### 控制函数
```c
void rs485_send_enable(void);        // 切换到发送模式
void rs485_receive_enable(void);     // 切换到接收模式
```

### 数据传输函数
```c
void rs485_send_data(uint8_t *data, uint16_t length);           // 发送数据
uint16_t rs485_receive_data(uint8_t *buffer, uint16_t max_length); // 接收数据
```

### 状态查询函数
```c
uint16_t rs485_get_rx_count(void);   // 获取接收数据长度
uint8_t rs485_is_frame_complete(void); // 检查帧是否完整
uint8_t* rs485_get_rx_buffer(void);  // 获取接收缓冲区
void rs485_clear_rx_buffer(void);    // 清除接收缓冲区
```

## 配置参数

- **波特率**: 9600 bps (可修改)
- **数据位**: 8位
- **停止位**: 1位  
- **校验位**: 无
- **流控制**: 无
- **中断优先级**: USART1_IRQn优先级1，子优先级0

## 使用示例

### 基本初始化
```c
#include "CMIC_GD32f470vet6.h"
#include "rs485_example.h"

int main(void)
{
    systick_config();           // 系统时钟配置
    rs485_example_init();       // RS485初始化
    
    while(1)
    {
        rs485_main_loop();      // 主循环处理
    }
}
```

### 发送数据
```c
uint8_t data[] = "Hello RS485!";
rs485_send_data(data, strlen((char*)data));
```

### 接收数据处理
```c
if(rs485_is_frame_complete())
{
    uint16_t len = rs485_get_rx_count();
    uint8_t* buffer = rs485_get_rx_buffer();
    
    // 处理接收到的数据
    process_data(buffer, len);
    
    // 清除缓冲区
    rs485_clear_rx_buffer();
}
```

## 中断处理

USART1中断处理函数已在`rs485_example.c`中实现：
- **RBNE中断**: 接收数据
- **IDLE中断**: 检测帧结束

## 测试方法

1. **硬件连接**: 按照上述引脚配置连接MAX3485
2. **编译下载**: 将程序下载到GD32F470VE
3. **串口工具**: 使用USB转RS485模块连接电脑
4. **测试通信**: 
   - 程序会定期发送心跳消息
   - 可发送命令如"LED_ON"、"LED_OFF"、"STATUS"等
   - 程序会回显接收到的数据

## 支持的命令

- `LED_ON` - 点亮LED
- `LED_OFF` - 熄灭LED  
- `STATUS` - 查询状态
- `ECHO:xxx` - 回显xxx内容
- 其他命令会被标记为UNKNOWN并回显

## 注意事项

1. **时序控制**: DE/RE切换有适当延时确保MAX3485正确切换
2. **中断优先级**: USART1中断优先级设置为1，避免与其他中断冲突
3. **缓冲区管理**: 接收缓冲区256字节，需及时处理避免溢出
4. **硬件连接**: 确保MAX3485供电正常，A+/B-使用双绞线连接
5. **终端电阻**: 长距离通信需在总线两端添加120Ω终端电阻

## 文件结构

```
2025923314-code/GD321/Components/bsp/
├── CMIC_GD32f470vet6.h          (已修改 - 添加RS485定义)
├── CMIC_GD32f470vet6.c          (已修改 - 添加RS485函数)
├── rs485_example.h              (新增 - RS485示例头文件)
├── rs485_example.c              (新增 - RS485示例实现)
├── main_rs485_demo.c            (新增 - 主程序演示)
├── RS485_README.md              (新增 - 详细说明文档)
└── UART1_RS485_Configuration_Summary.md (本文件)
```

配置已完成，可以开始使用UART1进行RS485通信。
