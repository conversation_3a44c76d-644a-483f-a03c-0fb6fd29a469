/*!
    \file    main_rs485_demo.c
    \brief   RS485 communication demo main program
    
    \version 2025-01-01
    \date    2025-01-01
    \author  MCUSTUDIO
*/

#include "gd32f4xx.h"
#include "systick.h"
#include "CMIC_GD32f470vet6.h"
#include "rs485_example.h"
#include <stdio.h>
#include <string.h>

/* LED控制 (如果有LED的话) */
#define LED_PIN     GPIO_PIN_13
#define LED_PORT    GPIOC

/*!
    \brief      configure LED
    \param[in]  none
    \param[out] none
    \retval     none
*/
void led_config(void)
{
    /* enable the LED clock */
    rcu_periph_clock_enable(RCU_GPIOC);
    
    /* configure LED GPIO pin */
    gpio_mode_set(LED_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, LED_PIN);
    gpio_output_options_set(LED_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, LED_PIN);
    
    /* turn off LED */
    gpio_bit_set(LED_PORT, LED_PIN);
}

/*!
    \brief      toggle LED
    \param[in]  none
    \param[out] none
    \retval     none
*/
void led_toggle(void)
{
    gpio_bit_toggle(LED_PORT, LED_PIN);
}

/*!
    \brief      process RS485 commands
    \param[in]  buffer: received data buffer
    \param[in]  length: data length
    \param[out] none
    \retval     none
*/
void process_rs485_command(uint8_t* buffer, uint16_t length)
{
    char response[64];
    
    /* 简单的命令处理示例 */
    if(strncmp((char*)buffer, "LED_ON", 6) == 0)
    {
        gpio_bit_reset(LED_PORT, LED_PIN);  // 点亮LED
        strcpy(response, "LED ON\r\n");
        rs485_send_data((uint8_t*)response, strlen(response));
    }
    else if(strncmp((char*)buffer, "LED_OFF", 7) == 0)
    {
        gpio_bit_set(LED_PORT, LED_PIN);    // 熄灭LED
        strcpy(response, "LED OFF\r\n");
        rs485_send_data((uint8_t*)response, strlen(response));
    }
    else if(strncmp((char*)buffer, "STATUS", 6) == 0)
    {
        sprintf(response, "GD32F470VE RS485 OK\r\n");
        rs485_send_data((uint8_t*)response, strlen(response));
    }
    else if(strncmp((char*)buffer, "ECHO:", 5) == 0)
    {
        /* 回显命令 */
        sprintf(response, "ECHO: %.*s\r\n", length-5, buffer+5);
        rs485_send_data((uint8_t*)response, strlen(response));
    }
    else
    {
        /* 未知命令，回显原数据 */
        sprintf(response, "UNKNOWN: %.*s\r\n", length, buffer);
        rs485_send_data((uint8_t*)response, strlen(response));
    }
}

/*!
    \brief      main function
    \param[in]  none
    \param[out] none
    \retval     none
*/
int main(void)
{
    uint32_t heartbeat_timer = 0;
    uint32_t led_timer = 0;
    
    /* configure systick */
    systick_config();
    
    /* configure LED */
    led_config();
    
    /* initialize RS485 communication */
    rs485_example_init();
    
    /* send startup message */
    delay_1ms(1000);  // 等待系统稳定
    rs485_send_data((uint8_t*)"GD32F470VE RS485 Demo Started\r\n", 32);
    
    while(1)
    {
        /* process received RS485 data */
        if(rs485_is_frame_complete())
        {
            uint16_t len = rs485_get_rx_count();
            uint8_t* buffer = rs485_get_rx_buffer();
            
            if(len > 0)
            {
                /* process command */
                process_rs485_command(buffer, len);
                
                /* clear receive buffer */
                rs485_clear_rx_buffer();
            }
        }
        
        /* heartbeat message every 10 seconds */
        if(heartbeat_timer++ >= 10000)
        {
            heartbeat_timer = 0;
            rs485_send_data((uint8_t*)"HEARTBEAT\r\n", 11);
        }
        
        /* toggle LED every 500ms */
        if(led_timer++ >= 500)
        {
            led_timer = 0;
            led_toggle();
        }
        
        /* delay 1ms */
        delay_1ms(1);
    }
}

/*!
    \brief      this function handles hard fault exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void HardFault_Handler(void)
{
    /* send error message if possible */
    rs485_send_data((uint8_t*)"HARD_FAULT_ERROR\r\n", 18);
    
    /* infinite loop */
    while(1)
    {
        led_toggle();
        delay_1ms(100);
    }
}

/*!
    \brief      this function handles memory manage exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void MemManage_Handler(void)
{
    /* send error message if possible */
    rs485_send_data((uint8_t*)"MEM_MANAGE_ERROR\r\n", 18);
    
    /* infinite loop */
    while(1)
    {
        led_toggle();
        delay_1ms(200);
    }
}

/*!
    \brief      this function handles bus fault exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void BusFault_Handler(void)
{
    /* send error message if possible */
    rs485_send_data((uint8_t*)"BUS_FAULT_ERROR\r\n", 17);
    
    /* infinite loop */
    while(1)
    {
        led_toggle();
        delay_1ms(300);
    }
}

/*!
    \brief      this function handles usage fault exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void UsageFault_Handler(void)
{
    /* send error message if possible */
    rs485_send_data((uint8_t*)"USAGE_FAULT_ERROR\r\n", 19);
    
    /* infinite loop */
    while(1)
    {
        led_toggle();
        delay_1ms(400);
    }
}
