/*!
    \file    rs485_example.c
    \brief   RS485 communication example using UART1 and MAX3485
    
    \version 2025-01-01
    \date    2025-01-01
    \author  MCUSTUDIO
*/

#include "CMIC_GD32f470vet6.h"
#include <string.h>

/* RS485 receive buffer */
static uint8_t rs485_rx_buffer[256];
static volatile uint16_t rs485_rx_count = 0;
static volatile uint8_t rs485_rx_complete = 0;

/*!
    \brief      USART1 interrupt service routine
    \param[in]  none
    \param[out] none
    \retval     none
*/
void USART1_IRQHandler(void)
{
    if(RESET != usart_interrupt_flag_get(USART1, USART_INT_FLAG_RBNE))
    {
        /* receive data */
        if(rs485_rx_count < sizeof(rs485_rx_buffer))
        {
            rs485_rx_buffer[rs485_rx_count] = (uint8_t)usart_data_receive(USART1);
            rs485_rx_count++;
        }
        
        /* clear RBNE flag */
        usart_interrupt_flag_clear(USART1, USART_INT_FLAG_RBNE);
    }
    
    if(RESET != usart_interrupt_flag_get(USART1, USART_INT_FLAG_IDLE))
    {
        /* idle line detected, frame complete */
        rs485_rx_complete = 1;
        
        /* clear IDLE flag by reading SR and DR */
        usart_flag_get(USART1, USART_FLAG_IDLE);
        usart_data_receive(USART1);
    }
}

/*!
    \brief      initialize RS485 communication
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rs485_example_init(void)
{
    /* initialize UART1 for RS485 */
    bsp_uart1_rs485_init();
    
    /* enable IDLE interrupt for frame detection */
    usart_interrupt_enable(USART1, USART_INT_IDLE);
    
    /* reset receive variables */
    rs485_rx_count = 0;
    rs485_rx_complete = 0;
}

/*!
    \brief      send test data via RS485
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rs485_send_test_data(void)
{
    uint8_t test_data[] = "Hello RS485 World!\r\n";
    
    rs485_send_data(test_data, strlen((char*)test_data));
}

/*!
    \brief      process received RS485 data
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rs485_process_received_data(void)
{
    if(rs485_rx_complete)
    {
        /* process received data */
        if(rs485_rx_count > 0)
        {
            /* echo back the received data */
            rs485_send_data(rs485_rx_buffer, rs485_rx_count);
            
            /* or process the data as needed */
            // your_data_processing_function(rs485_rx_buffer, rs485_rx_count);
        }
        
        /* reset for next frame */
        rs485_rx_count = 0;
        rs485_rx_complete = 0;
    }
}

/*!
    \brief      RS485 communication main loop example
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rs485_main_loop(void)
{
    static uint32_t send_timer = 0;
    
    /* send test data every 1000ms */
    if(send_timer++ >= 1000)
    {
        send_timer = 0;
        rs485_send_test_data();
    }
    
    /* process received data */
    rs485_process_received_data();
    
    /* delay 1ms */
    for(volatile uint32_t i = 0; i < 10000; i++);
}

/*!
    \brief      get received data count
    \param[in]  none
    \param[out] none
    \retval     received data count
*/
uint16_t rs485_get_rx_count(void)
{
    return rs485_rx_count;
}

/*!
    \brief      check if frame is complete
    \param[in]  none
    \param[out] none
    \retval     1: frame complete, 0: frame not complete
*/
uint8_t rs485_is_frame_complete(void)
{
    return rs485_rx_complete;
}

/*!
    \brief      get received data buffer
    \param[in]  none
    \param[out] none
    \retval     pointer to receive buffer
*/
uint8_t* rs485_get_rx_buffer(void)
{
    return rs485_rx_buffer;
}

/*!
    \brief      clear receive buffer
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rs485_clear_rx_buffer(void)
{
    rs485_rx_count = 0;
    rs485_rx_complete = 0;
    memset(rs485_rx_buffer, 0, sizeof(rs485_rx_buffer));
}
