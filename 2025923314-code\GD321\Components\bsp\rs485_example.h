/*!
    \file    rs485_example.h
    \brief   RS485 communication example header file
    
    \version 2025-01-01
    \date    2025-01-01
    \author  MCUSTUDIO
*/

#ifndef RS485_EXAMPLE_H
#define RS485_EXAMPLE_H

#include "gd32f4xx.h"

#ifdef __cplusplus
extern "C" {
#endif

/* function declarations */
void rs485_example_init(void);
void rs485_send_test_data(void);
void rs485_process_received_data(void);
void rs485_main_loop(void);
uint16_t rs485_get_rx_count(void);
uint8_t rs485_is_frame_complete(void);
uint8_t* rs485_get_rx_buffer(void);
void rs485_clear_rx_buffer(void);

/* interrupt handler */
void USART1_IRQHandler(void);

#ifdef __cplusplus
}
#endif

#endif /* RS485_EXAMPLE_H */
