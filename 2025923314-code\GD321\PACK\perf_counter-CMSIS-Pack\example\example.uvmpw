<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<ProjectWorkspace xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_mpw.xsd">

  <SchemaVersion>1.0</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <WorkspaceName>Y:\Work\git\perf_counter\example\example.uvmpw</WorkspaceName>

  <project>
    <PathAndName>.\example.uvprojx</PathAndName>
    <NodeIsActive>1</NodeIsActive>
    <NodeIsExpanded>1</NodeIsExpanded>
  </project>

  <project>
    <PathAndName>.\gcc_example.uvprojx</PathAndName>
  </project>

</ProjectWorkspace>
