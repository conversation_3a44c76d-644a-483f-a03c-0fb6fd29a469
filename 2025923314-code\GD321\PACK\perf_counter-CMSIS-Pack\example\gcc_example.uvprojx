<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>example</TargetName>
      <ToolsetNumber>0x3</ToolsetNumber>
      <ToolsetName>ARM-GNU</ToolsetName>
      <uAC6>1</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>ARMCM0</Device>
          <Vendor>ARM</Vendor>
          <PackID>ARM.Cortex_DFP.1.1.0</PackID>
          <PackURL>https://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x00020000) IROM(0x00000000,0x00040000) CPUTYPE("Cortex-M0") CLOCK(12000000) ESEL ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000)</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:ARMCM0$Device\ARM\ARMCM0\Include\ARMCM0.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile></SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\GCC_Out\</OutputDirectory>
          <OutputName>gcc_example</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>0</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>0</BrowseInformation>
          <ListingPath>.\GCC_Out\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments>  </SimDllArguments>
          <SimDlgDll>DARMCM1.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM0</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> </TargetDllArguments>
          <TargetDlgDll>TARMCM1.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM0</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>0</Capability>
            <DriverSelection>-1</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArm>
          <ArmMisc>
            <asLst>1</asLst>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>1</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <GCPUTYP>"Cortex-M0"</GCPUTYP>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <nBranchProt>0</nBranchProt>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x40000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <IRAM2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IRAM2>
              <IROM2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IROM2>
            </OnChipMemories>
          </ArmMisc>
          <Carm>
            <arpcs>1</arpcs>
            <stkchk>0</stkchk>
            <reentr>0</reentr>
            <interw>1</interw>
            <bigend>0</bigend>
            <Strict>0</Strict>
            <Optim>5</Optim>
            <wLevel>3</wLevel>
            <uThumb>1</uThumb>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>__PERF_COUNTER_CFG_USE_SYSTICK_WRAPPER__</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\perf_counter</IncludePath>
            </VariousControls>
          </Carm>
          <Aarm>
            <bBE>0</bBE>
            <interw>1</interw>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aarm>
          <LDarm>
            <umfTarg>1</umfTarg>
            <enaGarb>0</enaGarb>
            <noStart>0</noStart>
            <noStLib>0</noStLib>
            <uMathLib>1</uMathLib>
            <TextAddressRange></TextAddressRange>
            <DataAddressRange></DataAddressRange>
            <BSSAddressRange></BSSAddressRange>
            <IncludeLibs></IncludeLibs>
            <IncludeDir></IncludeDir>
            <Misc>-Wl,--wrap=SysTick_Handler

--specs=nosys.specs -Wl,--gc-sections
-mcpu=cortex-m0 -lc -ffunction-sections  -fdata-sections
-fshort-enums -fshort-wchar</Misc>
            <ScatterFile>.\gcc\gcc_arm.ld</ScatterFile>
          </LDarm>
        </TargetArm>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>startup</GroupName>
          <Files>
            <File>
              <FileName>startup_ARMCM0.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\gcc\startup_ARMCM0.c</FilePath>
            </File>
            <File>
              <FileName>system_ARMCM0.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\gcc\system_ARMCM0.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>application</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\main.c</FilePath>
            </File>
            <File>
              <FileName>platform.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform.c</FilePath>
            </File>
            <File>
              <FileName>stdout_USART.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\stdout_USART.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>perf_counter</GroupName>
          <Files>
            <File>
              <FileName>perf_counter.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\perf_counter.c</FilePath>
            </File>
            <File>
              <FileName>perf_counter.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\perf_counter.h</FilePath>
            </File>
            <File>
              <FileName>systick_wrapper_gcc.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\systick_wrapper_gcc.s</FilePath>
            </File>
            <File>
              <FileName>perfc_port_default.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\perfc_port_default.c</FilePath>
            </File>
            <File>
              <FileName>perfc_port_default.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\perfc_port_default.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>::CMSIS</GroupName>
        </Group>
      </Groups>
    </Target>
    <Target>
      <TargetName>gcc_lib</TargetName>
      <ToolsetNumber>0x3</ToolsetNumber>
      <ToolsetName>ARM-GNU</ToolsetName>
      <uAC6>1</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>ARMCM0</Device>
          <Vendor>ARM</Vendor>
          <PackID>ARM.Cortex_DFP.1.1.0</PackID>
          <PackURL>https://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x00020000) IROM(0x00000000,0x00040000) CPUTYPE("Cortex-M0") CLOCK(12000000) ESEL ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000)</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:ARMCM0$Device\ARM\ARMCM0\Include\ARMCM0.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile></SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\GCC_Out\</OutputDirectory>
          <OutputName>perf_counter_gcc</OutputName>
          <CreateExecutable>0</CreateExecutable>
          <CreateLib>1</CreateLib>
          <CreateHexFile>0</CreateHexFile>
          <DebugInformation>0</DebugInformation>
          <BrowseInformation>0</BrowseInformation>
          <ListingPath>.\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>copy.bat</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments>  </SimDllArguments>
          <SimDlgDll>DARMCM1.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM0</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> </TargetDllArguments>
          <TargetDlgDll>TARMCM1.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM0</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>-1</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArm>
          <ArmMisc>
            <asLst>1</asLst>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>1</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <GCPUTYP>"Cortex-M0"</GCPUTYP>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <nBranchProt>0</nBranchProt>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x40000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <IRAM2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IRAM2>
              <IROM2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IROM2>
            </OnChipMemories>
          </ArmMisc>
          <Carm>
            <arpcs>1</arpcs>
            <stkchk>0</stkchk>
            <reentr>0</reentr>
            <interw>1</interw>
            <bigend>0</bigend>
            <Strict>0</Strict>
            <Optim>4</Optim>
            <wLevel>3</wLevel>
            <uThumb>1</uThumb>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Carm>
          <Aarm>
            <bBE>0</bBE>
            <interw>1</interw>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aarm>
          <LDarm>
            <umfTarg>1</umfTarg>
            <enaGarb>0</enaGarb>
            <noStart>0</noStart>
            <noStLib>0</noStLib>
            <uMathLib>1</uMathLib>
            <TextAddressRange></TextAddressRange>
            <DataAddressRange></DataAddressRange>
            <BSSAddressRange></BSSAddressRange>
            <IncludeLibs></IncludeLibs>
            <IncludeDir></IncludeDir>
            <Misc>-Wl,--wrap=SysTick_Handler
--specs=nosys.specs -Wl,--gc-sections
-mcpu=cortex-m0 -lc -ffunction-sections  -fdata-sections
-fshort-enums -fshort-wchar</Misc>
            <ScatterFile>.\gcc\gcc_arm.ld</ScatterFile>
          </LDarm>
        </TargetArm>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>startup</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>0</IncludeInBuild>
              <AlwaysBuild>2</AlwaysBuild>
              <GenerateAssemblyFile>2</GenerateAssemblyFile>
              <AssembleAssemblyFile>2</AssembleAssemblyFile>
              <PublicsOnly>2</PublicsOnly>
              <StopOnExitCode>11</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>1</ComprImg>
            </CommonProperty>
            <GroupArm>
              <Carm>
                <arpcs>2</arpcs>
                <stkchk>2</stkchk>
                <reentr>2</reentr>
                <interw>2</interw>
                <bigend>2</bigend>
                <Strict>0</Strict>
                <Optim>0</Optim>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Carm>
              <Aarm>
                <bBE>2</bBE>
                <interw>2</interw>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aarm>
            </GroupArm>
          </GroupOption>
          <Files>
            <File>
              <FileName>startup_ARMCM0.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\gcc\startup_ARMCM0.c</FilePath>
            </File>
            <File>
              <FileName>system_ARMCM0.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\gcc\system_ARMCM0.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>application</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>0</IncludeInBuild>
              <AlwaysBuild>2</AlwaysBuild>
              <GenerateAssemblyFile>2</GenerateAssemblyFile>
              <AssembleAssemblyFile>2</AssembleAssemblyFile>
              <PublicsOnly>2</PublicsOnly>
              <StopOnExitCode>11</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>1</ComprImg>
            </CommonProperty>
            <GroupArm>
              <Carm>
                <arpcs>2</arpcs>
                <stkchk>2</stkchk>
                <reentr>2</reentr>
                <interw>2</interw>
                <bigend>2</bigend>
                <Strict>0</Strict>
                <Optim>0</Optim>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Carm>
              <Aarm>
                <bBE>2</bBE>
                <interw>2</interw>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aarm>
            </GroupArm>
          </GroupOption>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\main.c</FilePath>
            </File>
            <File>
              <FileName>platform.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\platform.c</FilePath>
            </File>
            <File>
              <FileName>stdout_USART.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\stdout_USART.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>perf_counter</GroupName>
          <Files>
            <File>
              <FileName>perf_counter.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\perf_counter.c</FilePath>
            </File>
            <File>
              <FileName>perf_counter.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\perf_counter.h</FilePath>
            </File>
            <File>
              <FileName>systick_wrapper_gcc.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\systick_wrapper_gcc.s</FilePath>
            </File>
            <File>
              <FileName>perfc_port_default.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\perfc_port_default.c</FilePath>
            </File>
            <File>
              <FileName>perfc_port_default.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\perfc_port_default.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>::CMSIS</GroupName>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components>
      <component Cclass="CMSIS" Cgroup="CORE" Cvendor="ARM" Cversion="5.6.0" condition="ARMv6_7_8-M Device">
        <package name="CMSIS" schemaVersion="1.7.7" url="http://www.keil.com/pack/" vendor="ARM" version="5.9.0"/>
        <targetInfos>
          <targetInfo name="example"/>
          <targetInfo name="gcc_lib"/>
        </targetInfos>
      </component>
    </components>
    <files>
      <file attr="config" category="linkerScript" condition="GCC" name="Device\ARM\ARMCM0\Source\GCC\gcc_arm.ld" version="2.1.0">
        <instance index="0" removed="1">RTE\Device\ARMCM0\gcc_arm.ld</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="ARM" Cversion="1.2.2" condition="ARMCM0 CMSIS"/>
        <package name="CMSIS" schemaVersion="1.3" url="http://www.keil.com/pack/" vendor="ARM" version="5.8.0"/>
        <targetInfos/>
      </file>
      <file attr="config" category="sourceAsm" condition="GCC" name="Device\ARM\ARMCM0\Source\GCC\startup_ARMCM0.S" version="0.0.0">
        <instance index="0" removed="1">RTE\Device\ARMCM0\startup_ARMCM0.S</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="ARM" Cversion="1.2.2" condition="ARMCM0 CMSIS"/>
        <package name="CMSIS" schemaVersion="1.3" url="http://www.keil.com/pack/" vendor="ARM" version="5.8.0"/>
        <targetInfos/>
      </file>
      <file attr="config" category="sourceC" name="Device\ARM\ARMCM0\Source\system_ARMCM0.c" version="0.0.0">
        <instance index="0" removed="1">RTE\Device\ARMCM0\system_ARMCM0.c</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="ARM" Cversion="1.2.2" condition="ARMCM0 CMSIS"/>
        <package name="CMSIS" schemaVersion="1.3" url="http://www.keil.com/pack/" vendor="ARM" version="5.8.0"/>
        <targetInfos/>
      </file>
      <file attr="config" category="header" name="CMSIS\Config\RTE_Device.h" version="0.0.0">
        <instance index="0" removed="1">RTE\Device\CMSDK_CM3\RTE_Device.h</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="Keil" Cversion="1.0.0" condition="CMSDK_CM3 CMSIS"/>
        <package name="V2M-MPS2_CMx_BSP" schemaVersion="1.2" url="http://www.keil.com/pack/" vendor="Keil" version="1.7.0"/>
        <targetInfos/>
      </file>
      <file attr="config" category="source" name="Device\CMSDK_CM3\Source\system_CMSDK_CM3.c" version="0.0.0">
        <instance index="0" removed="1">RTE\Device\CMSDK_CM3\system_CMSDK_CM3.c</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="Keil" Cversion="1.0.0" condition="CMSDK_CM3 CMSIS"/>
        <package name="V2M-MPS2_CMx_BSP" schemaVersion="1.2" url="http://www.keil.com/pack/" vendor="Keil" version="1.7.0"/>
        <targetInfos/>
      </file>
      <file attr="config" category="source" name="benchmark\coremark_port\core_portme.c" version="1.0.0">
        <instance index="0" removed="1">RTE\Utilities\core_portme.c</instance>
        <component Cbundle="Performance Counter" Cclass="Utilities" Cgroup="perf_counter" Csub="Benchmark" Cvariant="Coremark" Cvendor="GorgonMeducer" Cversion="1.0.0" condition="perf_counter"/>
        <package name="perf_counter" schemaVersion="1.4" url="https://raw.githubusercontent.com/GorgonMeducer/perf_counter/CMSIS-Pack/cmsis-pack/" vendor="GorgonMeducer" version="2.0.0"/>
        <targetInfos/>
      </file>
      <file attr="config" category="header" name="benchmark\coremark_port\core_portme.h" version="1.0.0">
        <instance index="0" removed="1">RTE\Utilities\core_portme.h</instance>
        <component Cbundle="Performance Counter" Cclass="Utilities" Cgroup="perf_counter" Csub="Benchmark" Cvariant="Coremark" Cvendor="GorgonMeducer" Cversion="1.0.0" condition="perf_counter"/>
        <package name="perf_counter" schemaVersion="1.4" url="https://raw.githubusercontent.com/GorgonMeducer/perf_counter/CMSIS-Pack/cmsis-pack/" vendor="GorgonMeducer" version="2.0.0"/>
        <targetInfos/>
      </file>
    </files>
  </RTE>

  <LayerInfo>
    <Layers>
      <Layer>
        <LayName>gcc_example</LayName>
        <LayPrjMark>1</LayPrjMark>
      </Layer>
    </Layers>
  </LayerInfo>

</Project>
