/* Licence
* Company: MCUSTUDIO
* Auther: <PERSON><PERSON><PERSON><PERSON>.
* Version: V0.10
* Time: 2025/05/15
* Note:
*/

//表中有部分AF对不上 具体看工程中的GD32F470手册

/*
 * GD32F470xx IO Multiplexing Table
 * This table lists the alternate functions (AF0 to AF15) for each pin across Ports A to I.
 * Format: PIN | AF0 | AF1 | AF2 | AF3 | AF4 | AF5 | AF6 | AF7 | AF8 | AF9 | AF10 | AF11 | AF12 | AF13 | AF14 | AF15
 * Note: Empty cells indicate no function assigned to that AF for the pin.
 *       Refer to the GD32F470xx datasheet (Tables 2-7 to 2-15) for detailed configuration.
 */

/*
 * Port A Alternate Functions
 *  PIN   | AF0                    | AF1                   | AF2               | AF3                  | AF4                  | AF5                | AF6                            | AF7                            | AF8                            | AF9                            | AF10        | AF11       | AF12      | AF13    | AF14      | AF15
 *  ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 *  PA0   | TIMER1_CH0/TIMER1_ETI  | TIMER4_CH0            | TIMER7_ETI        |                      | USART1_CTS           | UART3_TX           |                                | ENET_MII_CRS                   |                                |                                |             |            |           |         |           | EVENTOUT
 *  PA1   | TIMER1_CH1             | TIMER4_CH1            |                   | SPI3_MOSI            | USART1_RTS           | UART3_RX           |                                | ENET_MII_RX_CLK/ENET_RMII_REF_CLK |                             |                                |             |            |           |         |           | EVENTOUT
 *  PA2   | TIMER1_CH2             | TIMER4_CH2            | TIMER8_CH0        | I2S_CKIN             | USART1_TX            |                    | ENET_MDIO                      |                                |                                |                                |             |            |           |         |           | EVENTOUT
 *  PA3   | TIMER1_CH3             | TIMER4_CH3            | TIMER8_CH1        | I2S1_MCK             | USART1_RX            |                    | USBHS_ULPI_D0                  | ENET_MII_COL                   |                                |                                |             |            | TLI_B5    |         |           | EVENTOUT
 *  PA4   |                        |                       |                   | SPI0_NSS             | SPI2_NSS/I2S2_WS     | USART1_CK          |                                | USBHS_SOF                      | DCI_HSYNC                      | TLI_VSYNC                      |             |            |           |         |           | EVENTOUT
 *  PA5   | TIMER1_CH0/TIMER1_ETI  | TIMER7_CH0_ON         |                   | SPI0_SCK             |                      |                    | USBHS_ULPI_CK                  |                                |                                |                                |             |            |           |         |           | EVENTOUT
 *  PA6   | TIMER0_BRKIN           | TIMER2_CH0            | TIMER7_BRKIN      | SPI0_MISO            | I2S1_MCK             | TIMER12_CH0        | SDIO_CMD                       | DCI_PIXCLK                     | TLI_G2                         |                                |             |            |           |         |           | EVENTOUT
 *  PA7   | TIMER0_CH0_ON          | TIMER2_CH1            | TIMER7_CH0_ON     | SPI0_MOSI            |                      | TIMER13_CH0        | ENET_MII_RX_DV/ENET_RMII_CRS_DV| EXMC_SDNWE                     |                                |                                |             |            |           |         |           | EVENTOUT
 *  PA8   | CK_OUT0                | TIMER0_CH0            |                   | I2C2_SCL             | USART0_CK            | CTC_SYNC           | USBFS_SOF                      | SDIO_D1                        | TLI_R6                         |                                |             |            |           |         |           | EVENTOUT
 *  PA9   | TIMER0_CH1             |                       | I2C2_SMBA         | SPI1_SCK/I2S1_CK     | USART0_TX            |                    |                                | SDIO_D2                        | DCI_D0                         |                                |             |            |           |         |           | EVENTOUT
 *  PA10  | TIMER0_CH2             |                       | I2C2_TXFRAME      | SPI4_MOSI            | USART0_RX            |                    | USBFS_ID                       | DCI_D1                         |                                |                                |             |            |           |         |           | EVENTOUT
 *  PA11  | TIMER0_CH3             |                       |                   | SPI3_MISO            | USART0_CTS           | USART5_TX          | CAN0_RX                        | USBFS_DM                       |                                |                                |             | TLI_R4     |           |         |           | EVENTOUT
 *  PA12  | TIMER0_ETI             |                       |                   | SPI4_MISO            | USART0_RTS           | USART5_RX          | CAN0_TX                        | USBFS_DP                       |                                |                                |             | TLI_R5     |           |         |           | EVENTOUT
 *  PA13  | JTMS/SWDIO             |                       |                   |                      |                      |                    |                                |                                |                                |                                |             |            |           |         |           | EVENTOUT
 *  PA14  | JTCK/SWCLK             |                       |                   |                      |                      |                    |                                |                                |                                |                                |             |            |           |         |           | EVENTOUT
 *  PA15  | JTDI                   | TIMER1_CH0/TIMER1_ETI |                   | SPI0_NSS             | SPI2_NSS/I2S2_WS     | USART0_TX          |                                |                                |                                |                                |             |            |           |         |           | EVENTOUT
 */

/*
 * Port B Alternate Functions
 *  PIN   | AF0                    | AF1                   | AF2               | AF3                  | AF4                  | AF5                | AF6                            | AF7                            | AF8                            | AF9                            | AF10        | AF11       | AF12      | AF13    | AF14      | AF15
 *  ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 *  PB0   | TIMER0_CH1_ON          | TIMER2_CH2            | TIMER7_CH1_ON     |                      | SPI4_SCK             | SPI2_MOSI/I2S2_SD  | TLI_R3                         | USBHS_ULPI_D1                  | ENET_MII_RXD2                  | SDIO_D1                        |             |            |           |         |           | EVENTOUT
 *  PB1   | TIMER0_CH2_ON          | TIMER2_CH3            | TIMER7_CH2_ON     |                      | SPI4_NSS             |                    | TLI_R6                         | USBHS_ULPI_D2                  | ENET_MII_RXD3                  | SDIO_D2                        |             |            |           |         |           | EVENTOUT
 *  PB2   | TIMER1_CH3             |                       |                   | SPI2_MOSI/I2S2_SD    |                      |                    | USBHS_ULPI_D4                  | SDIO_CK                        |                                |                                |             |            |           |         |           | EVENTOUT
 *  PB3   | JTDO/TRACESWO          | TIMER1_CH1            |                   | SPI0_SCK             | SPI2_SCK/I2S2_CK     | USART0_RX          | I2C1_SDA                       |                                |                                |                                |             |            |           |         |           | EVENTOUT
 *  PB4   | NJTRST                 | TIMER2_CH0            | I2C0_TXFRAME      | SPI0_MISO            | SPI2_MISO            | I2S2_ADD_SD        | I2C2_SDA                       | SDIO_D0                        |                                |                                |             |            |           |         |           | EVENTOUT
 *  PB5   |                        | TIMER2_CH1            | I2C0_SMBA         | SPI0_MOSI            | SPI2_MOSI/I2S2_SD    | CAN1_RX            | USBHS_ULPI_D7                  | ENET_PPS_OUT                   | EXMC_SDCKE1                    | DCI_D10                        |             |            |           |         |           | EVENTOUT
 *  PB6   |                        | TIMER3_CH0            | I2C0_SCL          |                      | USART0_TX            | CAN1_TX            | EXMC_SDNE1                     | DCI_D5                         |                                |                                |             |            |           |         |           | EVENTOUT
 *  PB7   |                        | TIMER3_CH1            | I2C0_SDA          |                      | USART0_RX            | EXMC_NL/EXMC_NADV  | DCI_VSYNC                      |                                |                                |                                |             |            |           |         |           | EVENTOUT
 *  PB8   | TIMER1_CH0/TIMER1_ETI  | TIMER3_CH2            | TIMER9_CH0        | I2C0_SCL             | SPI4_MOSI            | CAN0_RX            | ENET_MII_TXD3                  | SDIO_D4                        | DCI_D6                         | TLI_B6                         |             |            |           |         |           | EVENTOUT
 *  PB9   | TIMER1_CH1             | TIMER3_CH3            | TIMER10_CH0       | I2C0_SDA             | SPI1_NSS/I2S1_WS     | CAN0_TX            | SDIO_D5                        | DCI_D7                         | TLI_B7                         |                                |             |            |           |         |           | EVENTOUT
 *  PB10  | TIMER1_CH2             |                       | I2C1_SCL          | SPI1_SCK/I2S1_CK     | I2S2_MCK             | USART2_TX          | USBHS_ULPI_D3                  | ENET_MII_RX_ER                 | SDIO_D7                        | TLI_G4                         |             |            |           |         |           | EVENTOUT
 *  PB11  | TIMER1_CH3             |                       | I2C1_SDA          | I2S_CKIN             | USART2_RX            | USBHS_ULPI_D4      | ENET_MII_TX_EN/ENET_RMII_TX_EN | TLI_G5                         |                                |                                |             |            |           |         |           | EVENTOUT
 *  PB12  | TIMER0_BRKIN           |                       | I2C1_SMBA         | SPI1_NSS/I2S1_WS     | SPI3_NSS             | USART2_CK          | CAN1_RX                        | USBHS_ULPI_D5                  | ENET_MII_TXD0/ENET_RMII_TXD0   | USBHS_ID                       |             |            |           |         |           | EVENTOUT
 *  PB13  | TIMER0_CH0_ON          |                       | I2C1_TXFRAME      | SPI1_SCK/I2S1_CK     | SPI3_SCK             | USART2_CTS         | CAN1_TX                        | USBHS_ULPI_D6                  | ENET_MII_TXD1/ENET_RMII_TXD1   |                                |             |            |           |         |           | EVENTOUT
 *  PB14  | TIMER0_CH1_ON          | TIMER7_CH1_ON         |                   | SPI1_MISO            | I2S1_ADD_SD          | USART2_RTS         | TIMER11_CH0                    | USBHS_DM                       |                                |                                |             |            |           |         |           | EVENTOUT
 *  PB15  | RTC_REFIN              | TIMER0_CH2_ON         | TIMER7_CH2_ON     |                      | SPI1_MOSI/I2S1_SD    | TIMER11_CH1        | USBHS_DP                       |                                |                                |                                |             |            |           |         |           | EVENTOUT
 */

/*
 * Port C Alternate Functions
 *  PIN   | AF0                    | AF1                   | AF2               | AF3                  | AF4                  | AF5                | AF6                            | AF7                            | AF8                            | AF9                            | AF10        | AF11       | AF12      | AF13    | AF14      | AF15
 *  ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 *  PC0   |                        |                       |                   |                      |                      |                    | USBHS_ULPI_STP                 | EXMC_SDNWE                     |                                |                                |             |            |           |         |           | EVENTOUT
 *  PC1   |                        |                       |                   |                      | SPI2_MOSI/I2S2_SD    | SPI1_MOSI/I2S1_SD  |                                | ENET_MDC                       |                                |                                |             |            |           |         |           | EVENTOUT
 *  PC2   |                        |                       |                   | SPI1_MISO            | I2S1_ADD_SD          |                    | USBHS_ULPI_DIR                 | ENET_MII_TXD2                  |                                | EXMC_SDNE0                     |             |            |           |         |           | EVENTOUT
 *  PC3   |                        |                       |                   | SPI1_MOSI/I2S1_SD    |                      |                    | USBHS_ULPI_NXT                 | ENET_MII_TX_CLK                |                                | EXMC_SDCKE0                    |             |            |           |         |           | EVENTOUT
 *  PC4   |                        |                       |                   |                      |                      |                    |                                | ENET_MII_RXD0/ENET_RMII_RXD0   |                                | EXMC_SDNE0                     |             |            |           |         |           | EVENTOUT
 *  PC5   |                        |                       |                   |                      |                      | USART2_RX          |                                | ENET_MII_RXD1/ENET_RMII_RXD1   |                                | EXMC_SDCKE0                    |             |            |           |         |           | EVENTOUT
 *  PC6   |                        | TIMER2_CH0            | TIMER7_CH0        | I2S1_MCK             |                      | USART5_TX          |                                |                                |                                | SDIO_D6                        | DCI_D0      | TLI_HSYNC  |           |         |           | EVENTOUT
 *  PC7   |                        | TIMER2_CH1            | TIMER7_CH1        | SPI1_SCK/I2S1_CK     | I2S2_MCK             | USART5_RX          |                                |                                |                                | SDIO_D7                        | DCI_D1      | TLI_G6     |           |         |           | EVENTOUT
 *  PC8   |                        | TIMER2_CH2            | TIMER7_CH2        |                      |                      | USART5_CK          |                                |                                |                                | SDIO_D0                        | DCI_D2      |            |           |         |           | EVENTOUT
 *  PC9   | CK_OUT1                | TIMER2_CH3            | TIMER7_CH3        | I2C2_SDA             | I2S_CKIN             |                    |                                |                                |                                | SDIO_D1                        | DCI_D3      |            |           |         |           | EVENTOUT
 *  PC10  |                        |                       |                   | SPI2_SCK/I2S2_CK     |                      | USART2_TX          | UART3_TX                       |                                |                                | SDIO_D2                        | DCI_D8      | TLI_R2     |           |         |           | EVENTOUT
 *  PC11  |                        |                       |                   | I2S2_ADD_SD          | SPI2_MISO            | USART2_RX          | UART3_RX                       |                                |                                | SDIO_D3                        | DCI_D4      |            |           |         |           | EVENTOUT
 *  PC12  |                        |                       | I2C1_SDA          |                      | SPI2_MOSI/I2S2_SD    | USART2_CK          | UART4_TX                       |                                |                                | SDIO_CK                        | DCI_D9      |            |           |         |           | EVENTOUT
 *  PC13  |                        |                       |                   |                      |                      |                    |                                |                                |                                |                                |             |            |           |         |           | EVENTOUT
 *  PC14  |                        |                       |                   |                      |                      |                    |                                |                                |                                |                                |             |            |           |         |           | EVENTOUT
 *  PC15  |                        |                       |                   |                      |                      |                    |                                |                                |                                |                                |             |            |           |         |           | EVENTOUT
 */

/*
 * Port D Alternate Functions
 *  PIN   | AF0                    | AF1                   | AF2               | AF3                  | AF4                  | AF5                | AF6                            | AF7                            | AF8                            | AF9                            | AF10        | AF11       | AF12      | AF13    | AF14      | AF15
 *  ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 *  PD0   |                        |                       |                   | SPI3_MISO            | SPI2_MOSI/I2S2_SD    |                    | CAN0_RX                        |                                |                                | EXMC_D2                        |             |            |           |         |           | EVENTOUT
 *  PD1   |                        |                       |                   | SPI1_NSS/I2S1_WS     |                      |                    | CAN0_TX                        |                                |                                | EXMC_D3                        |             |            |           |         |           | EVENTOUT
 *  PD2   |                        | TIMER2_ETI            |                   |                      |                      | UART4_RX           |                                | SDIO_CMD                       |                                | DCI_D11                        |             |            |           |         |           | EVENTOUT
 *  PD3   |                        |                       |                   | SPI1_SCK/I2S1_CK     | USART1_CTS           |                    |                                | EXMC_CLK                       |                                | DCI_D5                         | TLI_G7      |            |           |         |           | EVENTOUT
 *  PD4   |                        |                       |                   |                      | USART1_RTS           |                    |                                | EXMC_NOE                       |                                |                                |             |            |           |         |           | EVENTOUT
 *  PD5   |                        |                       |                   |                      | USART1_TX            |                    |                                | EXMC_NWE                       |                                |                                |             |            |           |         |           | EVENTOUT
 *  PD6   |                        |                       |                   | SPI2_MOSI/I2S2_SD    | USART1_RX            |                    |                                | EXMC_NWAIT                     |                                | DCI_D10                        | TLI_B2      |            |           |         |           | EVENTOUT
 *  PD7   |                        |                       |                   |                      | USART1_CK            |                    |                                | EXMC_NE0/EXMC_NCE1             |                                |                                |             |            |           |         |           | EVENTOUT
 *  PD8   |                        |                       |                   |                      | USART2_TX            |                    |                                | EXMC_D13                       |                                |                                |             |            |           |         |           | EVENTOUT
 *  PD9   |                        |                       |                   |                      | USART2_RX            |                    |                                | EXMC_D14                       |                                |                                |             |            |           |         |           | EVENTOUT
 *  PD10  |                        |                       |                   |                      | USART2_CK            |                    |                                | EXMC_D15                       |                                |                                | TLI_B3      |            |           |         |           | EVENTOUT
 *  PD11  |                        |                       |                   |                      | USART2_CTS           |                    |                                | EXMC_A16/EXMC_CLE              |                                |                                |             |            |           |         |           | EVENTOUT
 *  PD12  |                        | TIMER3_CH0            |                   |                      | USART2_RTS           |                    |                                | EXMC_A17/EXMC_ALE              |                                |                                |             |            |           |         |           | EVENTOUT
 *  PD13  |                        | TIMER3_CH1            |                   |                      |                      |                    |                                | EXMC_A18                       |                                |                                |             |            |           |         |           | EVENTOUT
 *  PD14  |                        | TIMER3_CH2            |                   |                      |                      |                    |                                | EXMC_D0                        |                                |                                |             |            |           |         |           | EVENTOUT
 *  PD15  | CTC_SYNC               | TIMER3_CH3            |                   |                      |                      |                    |                                | EXMC_D1                        |                                |                                |             |            |           |         |           | EVENTOUT
 */

/*
 * Port E Alternate Functions
 *  PIN   | AF0                    | AF1                   | AF2               | AF3                  | AF4                  | AF5                | AF6                            | AF7                            | AF8                            | AF9                            | AF10        | AF11       | AF12      | AF13    | AF14      | AF15
 *  ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 *  PE0   |                        | TIMER3_ETI            |                   |                      |                      | UART7_RX           |                                | EXMC_NBL0                      |                                | DCI_D2                         |             |            |           |         |           | EVENTOUT
 *  PE1   | TIMER0_CH1_ON          |                       |                   |                      |                      | UART7_TX           |                                | EXMC_NBL1                      |                                | DCI_D3                         |             |            |           |         |           | EVENTOUT
 *  PE2   |                        |                       |                   | SPI3_SCK             |                      |                    |                                | ENET_MII_TXD3                  |                                | EXMC_A23                       |             |            |           |         |           | EVENTOUT
 *  PE3   |                        |                       |                   |                      |                      |                    |                                | EXMC_A19                       |                                |                                |             |            |           |         |           | EVENTOUT
 *  PE4   |                        |                       |                   | SPI3_NSS             |                      |                    |                                | EXMC_A20                       |                                | DCI_D4                         | TLI_B0      |            |           |         |           | EVENTOUT
 *  PE5   |                        |                       | TIMER8_CH0        | SPI3_MISO            |                      |                    |                                | EXMC_A21                       |                                | DCI_D6                         | TLI_G0      |            |           |         |           | EVENTOUT
 *  PE6   |                        |                       | TIMER8_CH1        | SPI3_MOSI            |                      |                    |                                | EXMC_A22                       |                                | DCI_D7                         | TLI_G1      |            |           |         |           | EVENTOUT
 *  PE7   | TIMER0_ETI             |                       |                   |                      |                      | UART6_RX           |                                | EXMC_D4                        |                                |                                |             |            |           |         |           | EVENTOUT
 *  PE8   | TIMER0_CH0_ON          |                       |                   |                      |                      | UART6_TX           |                                | EXMC_D5                        |                                |                                |             |            |           |         |           | EVENTOUT
 *  PE9   | TIMER0_CH0             |                       |                   |                      |                      |                    |                                | EXMC_D6                        |                                |                                |             |            |           |         |           | EVENTOUT
 *  PE10  | TIMER0_CH1_ON          |                       |                   |                      |                      |                    |                                | EXMC_D7                        |                                |                                |             |            |           |         |           | EVENTOUT
 *  PE11  | TIMER0_CH1             |                       |                   | SPI3_NSS             | SPI4_NSS             |                    |                                | EXMC_D8                        |                                | TLI_G3                         |             |            |           |         |           | EVENTOUT
 *  PE12  | TIMER0_CH2_ON          |                       |                   | SPI3_SCK             | SPI4_SCK             |                    |                                | EXMC_D9                        |                                | TLI_B4                         |             |            |           |         |           | EVENTOUT
 *  PE13  | TIMER0_CH2             |                       |                   | SPI3_MISO            | SPI4_MISO            |                    |                                | EXMC_D10                       |                                | TLI_DE                         |             |            |           |         |           | EVENTOUT
 *  PE14  | TIMER0_CH3             |                       |                   | SPI3_MOSI            | SPI4_MOSI            |                    |                                | EXMC_D11                       |                                | TLI_PIXCLK                     |             |            |           |         |           | EVENTOUT
 *  PE15  | TIMER0_BRKIN           |                       |                   |                      |                      |                    |                                | EXMC_D12                       |                                | TLI_R7                         |             |            |           |         |           | EVENTOUT
 */

/*
 * Port F Alternate Functions
 *  PIN   | AF0                    | AF1                   | AF2               | AF3                  | AF4                  | AF5                | AF6                            | AF7                            | AF8                            | AF9                            | AF10        | AF11       | AF12      | AF13    | AF14      | AF15
 *  ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 *  PF0   | CTC_SYNC               |                       |                   | I2C1_SDA             |                      |                    |                                | EXMC_A0                        |                                |                                |             |            |           |         |           | EVENTOUT
 *  PF1   |                        |                       | I2C1_SCL          |                      |                      |                    |                                | EXMC_A1                        |                                |                                |             |            |           |         |           | EVENTOUT
 *  PF2   |                        |                       | I2C1_SMBA         |                      |                      |                    |                                | EXMC_A2                        |                                |                                |             |            |           |         |           | EVENTOUT
 *  PF3   |                        |                       | I2C1_TXFRAME      |                      |                      |                    |                                | EXMC_A3                        |                                |                                |             |            |           |         |           | EVENTOUT
 *  PF4   |                        |                       |                   |                      |                      |                    |                                | EXMC_A4                        |                                |                                |             |            |           |         |           | EVENTOUT
 *  PF5   |                        |                       |                   |                      |                      |                    |                                | EXMC_A5                        |                                |                                |             |            |           |         |           | EVENTOUT
 *  PF6   |                        |                       | TIMER9_CH0        |                      | SPI4_NSS             | UART6_RX           |                                | EXMC_NIORD                     |                                |                                |             |            |           |         |           | EVENTOUT
 *  PF7   |                        |                       | TIMER10_CH0       |                      | SPI4_SCK             | UART6_TX           |                                | EXMC_NREG                      |                                |                                |             |            |           |         |           | EVENTOUT
 *  PF8   |                        |                       |                   | SPI4_MISO            |                      | TIMER12_CH0        |                                | EXMC_NIOWR                     |                                |                                |             |            |           |         |           | EVENTOUT
 *  PF9   |                        |                       |                   | SPI4_MOSI            |                      | TIMER13_CH0        |                                | EXMC_CD                        |                                |                                |             |            |           |         |           | EVENTOUT
 *  PF10  |                        |                       |                   |                      |                      |                    |                                | EXMC_INTR                      |                                | DCI_D11                        | TLI_DE      |            |           |         |           | EVENTOUT
 *  PF11  |                        |                       |                   | SPI4_MOSI            |                      |                    |                                | EXMC_SDNRAS                    |                                | DCI_D12                        |             |            |           |         |           | EVENTOUT
 *  PF12  |                        |                       |                   |                      |                      |                    |                                | EXMC_A6                        |                                |                                |             |            |           |         |           | EVENTOUT
 *  PF13  |                        |                       |                   |                      |                      |                    |                                | EXMC_A7                        |                                |                                |             |            |           |         |           | EVENTOUT
 *  PF14  |                        |                       |                   |                      |                      |                    |                                | EXMC_A8                        |                                |                                |             |            |           |         |           | EVENTOUT
 *  PF15  |                        |                       |                   |                      |                      |                    |                                | EXMC_A9                        |                                |                                |             |            |           |         |           | EVENTOUT
 */

/*
 * Port G Alternate Functions
 *  PIN   | AF0                    | AF1                   | AF2               | AF3                  | AF4                  | AF5                | AF6                            | AF7                            | AF8                            | AF9                            | AF10        | AF11       | AF12      | AF13    | AF14      | AF15
 *  ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 *  PG0   |                        |                       |                   |                      |                      |                    |                                | EXMC_A10                       |                                |                                |             |            |           |         |           | EVENTOUT
 *  PG1   |                        |                       |                   |                      |                      |                    |                                | EXMC_A11                       |                                |                                |             |            |           |         |           | EVENTOUT
 *  PG2   |                        |                       |                   |                      |                      |                    |                                | EXMC_A12                       |                                |                                |             |            |           |         |           | EVENTOUT
 *  PG3   |                        |                       |                   |                      |                      |                    |                                | EXMC_A13                       |                                |                                |             |            |           |         |           | EVENTOUT
 *  PG4   |                        |                       |                   |                      |                      |                    |                                | EXMC_A14                       |                                |                                |             |            |           |         |           | EVENTOUT
 *  PG5   |                        |                       |                   |                      |                      |                    |                                | EXMC_A15                       |                                |                                |             |            |           |         |           | EVENTOUT
 *  PG6   |                        |                       |                   |                      |                      |                    |                                | EXMC_INT1                      |                                | DCI_D12                        | TLI_R7      |            |           |         |           | EVENTOUT
 *  PG7   |                        |                       |                   |                      |                      | USART5_CK          |                                | EXMC_INT2                      |                                | DCI_D13                        | TLI_PIXCLK  |            |           |         |           | EVENTOUT
 *  PG8   |                        |                       |                   | SPI5_NSS             |                      | USART5_RTS         |                                | ENET_PPS_OUT                   | EXMC_SDCLK                     |                                |             |            |           |         |           | EVENTOUT
 *  PG9   |                        |                       |                   |                      |                      | USART5_RX          |                                | EXMC_NE1/EXMC_NCE2             |                                | DCI_VSYNC                      |             |            |           |         |           | EVENTOUT
 *  PG10  |                        |                       |                   | SPI5_IO2             |                      |                    | TLI_G3                         | EXMC_NCE3_0/EXMC_NE2           |                                | DCI_D2                         | TLI_B2      |            |           |         |           | EVENTOUT
 *  PG11  |                        |                       |                   | SPI5_IO3             | SPI3_SCK             |                    |                                | ENET_MII_TX_EN/ENET_RMII_TX_EN | EXMC_NCE3_1                    | DCI_D3                         | TLI_B3      |            |           |         |           | EVENTOUT
 *  PG12  |                        |                       |                   | SPI5_MISO            | SPI3_MISO            | USART5_RTS         | TLI_B4                         | EXMC_NE3                       |                                |                                | TLI_B1      |            |           |         |           | EVENTOUT
 *  PG13  |                        |                       |                   | SPI5_SCK             | SPI3_MOSI            | USART5_CTS         |                                | ENET_MII_TXD0/ENET_RMII_TXD0   | EXMC_A24                       |                                |             |            |           |         |           | EVENTOUT
 *  PG14  |                        |                       |                   | SPI5_MOSI            | SPI3_NSS             | USART5_TX          |                                | ENET_MII_TXD1/ENET_RMII_TXD1   | EXMC_A25                       |                                |             |            |           |         |           | EVENTOUT
 *  PG15  |                        |                       |                   |                      |                      | USART5_CTS         |                                | EXMC_SDNCAS                    |                                | DCI_D13                        |             |            |           |         |           | EVENTOUT
 */

/*
 * Port H Alternate Functions
 *  PIN   | AF0                    | AF1                   | AF2               | AF3                  | AF4                  | AF5                | AF6                            | AF7                            | AF8                            | AF9                            | AF10        | AF11       | AF12      | AF13    | AF14      | AF15
 *  ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 *  PH0   |                        |                       |                   |                      |                      |                    |                                |                                |                                |                                |             |            |           |         |           | EVENTOUT
 *  PH1   |                        |                       |                   |                      |                      |                    |                                |                                |                                |                                |             |            |           |         |           | EVENTOUT
 *  PH2   |                        |                       |                   |                      |                      |                    |                                | ENET_MII_CRS                   | EXMC_SDCKE0                    | TLI_R0                         |             |            |           |         |           | EVENTOUT
 *  PH3   |                        |                       | I2C1_TXFRAME      |                      |                      |                    |                                | ENET_MII_COL                   | EXMC_SDNE0                     | TLI_R1                         |             |            |           |         |           | EVENTOUT
 *  PH4   |                        |                       | I2C1_SCL          |                      |                      |                    | USBHS_ULPI_NXT                 |                                |                                |                                |             |            |           |         |           | EVENTOUT
 *  PH5   |                        |                       | I2C1_SDA          |                      | SPI4_NSS             |                    |                                | EXMC_SDNWE                     |                                |                                |             |            |           |         |           | EVENTOUT
 *  PH6   |                        |                       | I2C1_SMBA         |                      | SPI4_SCK             |                    | TIMER11_CH0                    | ENET_MII_RXD2                  | EXMC_SDNE1                     | DCI_D8                         |             |            |           |         |           | EVENTOUT
 *  PH7   |                        |                       | I2C2_SCL          |                      | SPI4_MISO            |                    |                                | ENET_MII_RXD3                  | EXMC_SDCKE1                    | DCI_D9                         |             |            |           |         |           | EVENTOUT
 *  PH8   |                        |                       | I2C2_SDA          |                      |                      |                    |                                | EXMC_D16                       |                                | DCI_HSYNC                      | TLI_R2      |            |           |         |           | EVENTOUT
 *  PH9   |                        |                       | I2C2_SMBA         |                      |                      |                    | TIMER11_CH1                    | EXMC_D17                       |                                | DCI_D0                         | TLI_R3      |            |           |         |           | EVENTOUT
 *  PH10  |                        | TIMER4_CH0            | I2C2_TXFRAME      |                      |                      |                    |                                | EXMC_D18                       |                                | DCI_D1                         | TLI_R4      |            |           |         |           | EVENTOUT
 *  PH11  |                        | TIMER4_CH1            |                   |                      |                      |                    |                                | EXMC_D19                       |                                | DCI_D2                         | TLI_R5      |            |           |         |           | EVENTOUT
 *  PH12  |                        | TIMER4_CH2            |                   |                      |                      |                    |                                | EXMC_D20                       |                                | DCI_D3                         | TLI_R6      |            |           |         |           | EVENTOUT
 *  PH13  |                        |                       | TIMER7_CH0_ON     |                      |                      |                    | CAN0_TX                        | EXMC_D21                       |                                | TLI_G2                         |             |            |           |         |           | EVENTOUT
 *  PH14  |                        |                       | TIMER7_CH1_ON     |                      |                      |                    |                                | EXMC_D22                       |                                | DCI_D4                         | TLI_G3      |            |           |         |           | EVENTOUT
 *  PH15  |                        |                       | TIMER7_CH2_ON     |                      |                      |                    |                                | EXMC_D23                       |                                | DCI_D11                        | TLI_G4      |            |           |         |           | EVENTOUT
 */

/*
 * Port I Alternate Functions
 *  PIN   | AF0                    | AF1                   | AF2               | AF3                  | AF4                  | AF5                | AF6                            | AF7                            | AF8                            | AF9                            | AF10        | AF11       | AF12      | AF13    | AF14      | AF15
 *  ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 *  PI0   |                        | TIMER4_CH3            |                   | SPI1_NSS/I2S1_WS     |                      |                    |                                | EXMC_D24                       |                                | DCI_D13                        | TLI_G5      |            |           |         |           | EVENTOUT
 *  PI1   |                        |                       |                   | SPI1_SCK/I2S1_CK     |                      |                    |                                | EXMC_D25                       |                                | DCI_D8                         | TLI_G6      |            |           |         |           | EVENTOUT
 *  PI2   |                        |                       | TIMER7_CH3        | SPI1_MISO            | I2S1_ADD_SD          |                    |                                | EXMC_D26                       |                                | DCI_D9                         | TLI_G7      |            |           |         |           | EVENTOUT
 *  PI3   |                        |                       | TIMER7_ETI        | SPI1_MOSI/I2S1_SD    |                      |                    |                                | EXMC_D27                       |                                | DCI_D10                        |             |            |           |         |           | EVENTOUT
 *  PI4   |                        |                       | TIMER7_BRKIN      |                      |                      |                    |                                | EXMC_NBL2                      |                                | DCI_D5                         | TLI_B4      |            |           |         |           | EVENTOUT
 *  PI5   |                        |                       | TIMER7_CH0        |                      |                      |                    |                                | EXMC_NBL3                      |                                | DCI_VSYNC                      | TLI_B5      |            |           |         |           | EVENTOUT
 *  PI6   |                        |                       | TIMER7_CH1        |                      |                      |                    |                                | EXMC_D28                       |                                | DCI_D6                         | TLI_B6      |            |           |         |           | EVENTOUT
 *  PI7   |                        |                       | TIMER7_CH2        |                      |                      |                    |                                | EXMC_D29                       |                                | DCI_D7                         | TLI_B7      |            |           |         |           | EVENTOUT
 *  PI8   |                        |                       |                   |                      |                      |                    |                                |                                |                                |                                |             |            |           |         |           | EVENTOUT
 *  PI9   |                        |                       |                   |                      |                      |                    | CAN0_RX                        | EXMC_D30                       |                                |                                | TLI_VSYNC   |            |           |         |           | EVENTOUT
 *  PI10  |                        |                       |                   |                      |                      |                    |                                | ENET_MII_RX_ER                 | EXMC_D31                       |                                | TLI_HSYNC   |            |           |         |           | EVENTOUT
 *  PI11  |                        |                       |                   |                      |                      |                    | USBHS_ULPI_DIR                 |                                |                                |                                |             |            |           |         |           | EVENTOUT
 */
