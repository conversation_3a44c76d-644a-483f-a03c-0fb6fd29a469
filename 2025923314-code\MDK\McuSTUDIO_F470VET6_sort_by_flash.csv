File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
c_w.l,19.022957%,12819,96,12268,551,0,96
usart_app.o,9.055159%,6102,556,6070,0,32,524
sdio_sdcard.o,9.022511%,6080,64,6048,0,32,32
ff.o,8.786561%,5921,8,5900,13,8,0
oled.o,5.742948%,3870,22,1136,2712,22,0
btod.o,3.193494%,2152,0,2152,0,0,0
sd_app.o,3.131168%,2110,2756,2106,0,4,2752
ebtn.o,2.525710%,1702,60,1702,0,0,60
fz_wm.l,2.258596%,1522,0,1506,16,0,0
scanf_fp.o,1.887604%,1272,0,1272,0,0,0
cmic_gd32f470vet6.o,1.854957%,1250,574,1232,0,18,556
_printf_fp_dec.o,1.564100%,1054,0,1054,0,0,0
gd25qxx.o,1.323697%,892,0,892,0,0,0
_scanf.o,1.311826%,884,0,884,0,0,0
m_wm.l,1.190140%,802,0,802,0,0,0
_printf_fp_hex.o,1.190140%,802,0,764,38,0,0
scanf_hexfp.o,1.187173%,800,0,800,0,0,0
perf_counter.o,1.104071%,744,80,660,4,80,0
gd32f4xx_rcu.o,1.098135%,740,0,740,0,0,0
gd32f4xx_dma.o,1.041744%,702,0,702,0,0,0
gd32f4xx_usart.o,0.851796%,574,0,574,0,0,0
gd32f4xx_sdio.o,0.810245%,546,0,546,0,0,0
btn_app.o,0.777598%,524,140,370,14,140,0
gd32f4xx_adc.o,0.774630%,522,0,522,0,0,0
system_gd32f4xx.o,0.768694%,518,4,514,0,4,0
startup_gd32f450_470.o,0.730111%,492,2048,64,428,0,2048
rtc_app.o,0.712304%,480,0,480,0,0,0
unicode.o,0.667785%,450,0,74,376,0,0
gd32f4xx_i2c.o,0.649977%,438,0,438,0,0,0
__printf_flags_ss_wp.o,0.606942%,409,0,392,17,0,0
gd32f4xx_rtc.o,0.605458%,408,0,408,0,0,0
bigflt0.o,0.557971%,376,0,228,148,0,0
dmul.o,0.504548%,340,0,340,0,0,0
_scanf_int.o,0.492677%,332,0,332,0,0,0
lc_ctype_c.o,0.468933%,316,0,44,272,0,0
scanf_infnan.o,0.457061%,308,0,308,0,0,0
oled_app.o,0.445190%,300,0,300,0,0,0
gd32f4xx_gpio.o,0.415510%,280,0,280,0,0,0
narrow.o,0.394735%,266,0,266,0,0,0
diskio.o,0.373959%,252,0,252,0,0,0
lludivv7m.o,0.353184%,238,0,238,0,0,0
ldexp.o,0.338344%,228,0,228,0,0,0
_printf_wctomb.o,0.290857%,196,0,188,8,0,0
_printf_hex_int_ll_ptr.o,0.278986%,188,0,148,40,0,0
adc_app.o,0.267114%,180,8,172,0,8,0
_printf_intcommon.o,0.264146%,178,0,178,0,0,0
gd32f4xx_misc.o,0.261178%,176,0,176,0,0,0
strtod.o,0.243370%,164,0,164,0,0,0
dnaninf.o,0.231499%,156,0,156,0,0,0
strncmp.o,0.222595%,150,0,150,0,0,0
systick.o,0.219627%,148,4,144,0,4,0
gd32f4xx_it.o,0.219627%,148,0,148,0,0,0
main.o,0.210723%,142,0,142,0,0,0
frexp.o,0.207755%,140,0,140,0,0,0
fnaninf.o,0.207755%,140,0,140,0,0,0
rt_memcpy_v6.o,0.204787%,138,0,138,0,0,0
lludiv10.o,0.204787%,138,0,138,0,0,0
scheduler.o,0.201819%,136,64,72,0,64,0
strcmpv7m.o,0.189948%,128,0,128,0,0,0
_printf_fp_infnan.o,0.189948%,128,0,128,0,0,0
_printf_longlong_dec.o,0.184012%,124,0,124,0,0,0
perfc_port_default.o,0.181044%,122,0,122,0,0,0
dleqf.o,0.178076%,120,0,120,0,0,0
deqf.o,0.178076%,120,0,120,0,0,0
_printf_dec.o,0.178076%,120,0,120,0,0,0
_printf_oct_int_ll.o,0.166204%,112,0,112,0,0,0
drleqf.o,0.160268%,108,0,108,0,0,0
gd32f4xx_spi.o,0.154332%,104,0,104,0,0,0
retnan.o,0.148397%,100,0,100,0,0,0
rt_memcpy_w.o,0.148397%,100,0,100,0,0,0
d2f.o,0.145429%,98,0,98,0,0,0
scalbn.o,0.136525%,92,0,92,0,0,0
__dczerorl2.o,0.133557%,90,0,90,0,0,0
memcmp.o,0.130589%,88,0,88,0,0,0
f2d.o,0.127621%,86,0,86,0,0,0
strncpy.o,0.127621%,86,0,86,0,0,0
_printf_str.o,0.121685%,82,0,82,0,0,0
rt_memclr_w.o,0.115749%,78,0,78,0,0,0
_printf_pad.o,0.115749%,78,0,78,0,0,0
sys_stackheap_outer.o,0.109813%,74,0,74,0,0,0
llsdiv.o,0.106846%,72,0,72,0,0,0
lc_numeric_c.o,0.106846%,72,0,44,28,0,0
rt_memclr.o,0.100910%,68,0,68,0,0,0
dunder.o,0.094974%,64,0,64,0,0,0
_wcrtomb.o,0.094974%,64,0,64,0,0,0
_sgetc.o,0.094974%,64,0,64,0,0,0
strlen.o,0.092006%,62,0,62,0,0,0
__0sscanf.o,0.089038%,60,0,60,0,0,0
atof.o,0.083102%,56,0,56,0,0,0
vsnprintf.o,0.077166%,52,0,52,0,0,0
__scatter.o,0.077166%,52,0,52,0,0,0
fpclassify.o,0.071230%,48,0,48,0,0,0
trapv.o,0.071230%,48,0,48,0,0,0
_printf_char_common.o,0.071230%,48,0,48,0,0,0
scanf_char.o,0.065294%,44,0,44,0,0,0
_printf_wchar.o,0.065294%,44,0,44,0,0,0
_printf_char.o,0.065294%,44,0,44,0,0,0
__2sprintf.o,0.065294%,44,0,44,0,0,0
_printf_charcount.o,0.059359%,40,0,40,0,0,0
llshl.o,0.056391%,38,0,38,0,0,0
libinit2.o,0.056391%,38,0,38,0,0,0
strstr.o,0.053423%,36,0,36,0,0,0
init_aeabi.o,0.053423%,36,0,36,0,0,0
_printf_truncate.o,0.053423%,36,0,36,0,0,0
systick_wrapper_ual.o,0.047487%,32,0,32,0,0,0
_chval.o,0.041551%,28,0,28,0,0,0
__scatter_zi.o,0.041551%,28,0,28,0,0,0
dcmpi.o,0.035615%,24,0,24,0,0,0
_rserrno.o,0.032647%,22,0,22,0,0,0
strchr.o,0.029679%,20,0,20,0,0,0
isspace.o,0.026711%,18,0,18,0,0,0
exit.o,0.026711%,18,0,18,0,0,0
fpconst.o,0.023743%,16,0,0,16,0,0
dcheck1.o,0.023743%,16,0,16,0,0,0
rt_ctype_table.o,0.023743%,16,0,16,0,0,0
_snputc.o,0.023743%,16,0,16,0,0,0
gd32f4xx_pmu.o,0.023743%,16,0,16,0,0,0
__printf_wp.o,0.020776%,14,0,14,0,0,0
dretinf.o,0.017808%,12,0,12,0,0,0
sys_exit.o,0.017808%,12,0,12,0,0,0
__rtentry2.o,0.017808%,12,0,12,0,0,0
fretinf.o,0.014840%,10,0,10,0,0,0
fpinit.o,0.014840%,10,0,10,0,0,0
rtexit2.o,0.014840%,10,0,10,0,0,0
_sputc.o,0.014840%,10,0,10,0,0,0
_printf_ll.o,0.014840%,10,0,10,0,0,0
_printf_l.o,0.014840%,10,0,10,0,0,0
scanf2.o,0.011872%,8,0,8,0,0,0
rt_locale_intlibspace.o,0.011872%,8,0,8,0,0,0
rt_errno_addr_intlibspace.o,0.011872%,8,0,8,0,0,0
libspace.o,0.011872%,8,96,8,0,0,96
__main.o,0.011872%,8,0,8,0,0,0
istatus.o,0.008904%,6,0,6,0,0,0
heapauxi.o,0.008904%,6,0,6,0,0,0
_printf_x.o,0.008904%,6,0,6,0,0,0
_printf_u.o,0.008904%,6,0,6,0,0,0
_printf_s.o,0.008904%,6,0,6,0,0,0
_printf_p.o,0.008904%,6,0,6,0,0,0
_printf_o.o,0.008904%,6,0,6,0,0,0
_printf_n.o,0.008904%,6,0,6,0,0,0
_printf_ls.o,0.008904%,6,0,6,0,0,0
_printf_llx.o,0.008904%,6,0,6,0,0,0
_printf_llu.o,0.008904%,6,0,6,0,0,0
_printf_llo.o,0.008904%,6,0,6,0,0,0
_printf_lli.o,0.008904%,6,0,6,0,0,0
_printf_lld.o,0.008904%,6,0,6,0,0,0
_printf_lc.o,0.008904%,6,0,6,0,0,0
_printf_i.o,0.008904%,6,0,6,0,0,0
_printf_g.o,0.008904%,6,0,6,0,0,0
_printf_f.o,0.008904%,6,0,6,0,0,0
_printf_e.o,0.008904%,6,0,6,0,0,0
_printf_d.o,0.008904%,6,0,6,0,0,0
_printf_c.o,0.008904%,6,0,6,0,0,0
_printf_a.o,0.008904%,6,0,6,0,0,0
__rtentry4.o,0.008904%,6,0,6,0,0,0
scanf1.o,0.005936%,4,0,4,0,0,0
printf2.o,0.005936%,4,0,4,0,0,0
printf1.o,0.005936%,4,0,4,0,0,0
_printf_percent_end.o,0.005936%,4,0,4,0,0,0
use_no_semi.o,0.002968%,2,0,2,0,0,0
rtexit.o,0.002968%,2,0,2,0,0,0
libshutdown2.o,0.002968%,2,0,2,0,0,0
libshutdown.o,0.002968%,2,0,2,0,0,0
libinit.o,0.002968%,2,0,2,0,0,0
