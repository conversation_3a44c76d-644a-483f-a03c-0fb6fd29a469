# GD32F470 嵌入式开发模板工程

## 项目简介

本工程是基于西门子嵌入式大赛的GD32F470VET6微控制器开发模板，采用GD32标准固件库实现。该工程为简洁版，提供了丰富的外设驱动和应用示例，方便开发者快速上手GD32系列微控制器的开发。

## 硬件平台
- 主控芯片: GD32F470VET6 (ARM Cortex-M4, 主频高达200MHz)
- 开发板: CMIC_GD32F470VET6 (西门子嵌入式大赛定制板)
- 核心外设:
  - LED (6个用户LED)
  - 按键 (6个用户按键+1个复位键)
  - OLED显示屏 (0.91英寸, SSD1306, I2C接口)
  - SPI Flash (GD25QXX系列)
  - SDIO接口 (支持SD卡)
  - USART串口接口
  - ADC模拟量采集
  - DAC模拟量输出

## 软件架构

本工程采用层次化的软件架构设计:

1. **驱动层 (Driver)**
   - GD32F4XX标准固件库
   - CMSIS核心库

2. **组件层 (Components)**
   - `bsp`: 板级支持包，包含硬件初始化和配置
   - `ebtn`: 增强型按键处理库，支持多种按键检测模式
   - `gd25qxx`: SPI Flash驱动，支持标准读写及DMA传输模式
   - `oled`: OLED显示驱动，支持文本、图形和中文显示
   - `sdio`: SD卡驱动接口

3. **应用层 (APP)**
   - `btn_app`: 按键应用处理
   - `led_app`: LED控制应用
   - `oled_app`: OLED显示应用
   - `usart_app`: 串口通信应用
   - `scheduler`: 简易任务调度器

## 功能特性

1. **多功能驱动支持**
   - GPIO直接控制和位带操作
   - 基于DMA的高效SPI通信
   - I2C通信带有总线复位机制
   - SDIO高速接口
   - 12位DAC和ADC转换

2. **精细化定时控制**
   - 高精度性能计数器
   - 多级中断优先级管理
   - 定时器精确触发机制

3. **用户应用示例**
   - LED闪烁和控制模式
   - 按键检测与去抖
   - OLED菜单系统
   - 串口命令交互

## 快速开始

### 开发环境要求
- MDK-ARM 5.25以上版本
- IAR EWARM 8.20以上版本
- GCC ARM工具链 (可选)

### 编译和下载 (MDK-ARM)
1. 打开`MDK/Project.uvprojx`工程文件
2. 编译工程: Project -> Rebuild all target files
3. 下载程序: Debug -> Start/Stop Debug Session
4. 运行程序: Debug -> Run (F5)

### 编译和下载 (IAR EWARM)
1. 打开EWARM工程文件
2. 编译所有文件: Project -> Rebuild all
3. 载入工程镜像: Project -> Debug
4. 运行程序: Debug -> Go (F5)

## 自定义开发

本工程为简洁版模板，供开发者修改和扩展。以下是一些常见的自定义方向:

1. **添加新的外设驱动**
   - 在`Components`目录下创建新的驱动文件夹
   - 参考现有驱动结构实现接口

2. **扩展应用功能**
   - 在`APP`目录添加新的应用模块
   - 在`scheduler.c`中注册新的任务

3. **修改系统配置**
   - 时钟配置位于`USER/src/main.c`
   - 中断优先级配置可在各驱动初始化函数中调整

## 注意事项

1. 首次使用请仔细阅读`Docs`目录中的文档，特别是芯片数据手册和硬件原理图
2. 使用DMA时注意通道冲突，本工程已对各外设使用的DMA通道进行了合理分配
3. 代码中提供了多种外设使用的示例，包括轮询、中断和DMA模式
4. OLED和SPI Flash等器件的初始化函数中增加了复位和超时机制，提高了系统稳定性

## 高级功能示例

### SPI Flash DMA传输
本工程实现了SPI Flash的DMA传输功能，大幅提升了数据传输速度，特别是对于大块数据的读写操作。

### OLED I2C通信总线复位
针对I2C通信可能出现的总线锁死问题，实现了软件复位机制，提高了OLED显示的稳定性。

### DAC波形输出
支持DAC输出各种波形，可用于信号发生器或模拟量控制场景。

## 版权和许可

本工程由McuSTUDIO团队开发，基于西门子嵌入式大赛平台。仅供学习和非商业用途使用。GD32固件库版权归兆易创新(GigaDevice)所有。

## 联系方式

如有问题或建议，请联系项目维护者。

---

*最后更新于2025年6月5日*
